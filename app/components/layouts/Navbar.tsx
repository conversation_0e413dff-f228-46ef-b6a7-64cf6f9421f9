"use client"
import { useState, useEffect } from 'react';
import {
  Gift,
  Search,
  MapPin,
  Star,
  MoreHorizontal,
  Menu,
  X,
  User,
  Globe,
  ChevronDown
} from 'lucide-react';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isPreferencesOpen, setIsPreferencesOpen] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [selectedLanguage, setSelectedLanguage] = useState('EN');

  // Force light mode on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const htmlElement = document.documentElement;
      const bodyElement = document.body;

      // Remove any dark mode classes
      htmlElement.classList.remove('dark', 'dark-mode');
      htmlElement.classList.add('light', 'light-mode');
      htmlElement.setAttribute('data-theme', 'light');
      htmlElement.style.colorScheme = 'light';

      // Set localStorage to light mode
      localStorage.setItem('theme', 'light');
    }
  }, []);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { icon: Search, text: "Discover", href: "#discover" },
    { icon: MapPin, text: "Tours", href: "#tours" },
    { icon: Star, text: "Experiences", href: "#experiences" },
    { icon: Gift, text: "Rewards", href: "#rewards" }
  ];

  return (
    <nav className={`fixed w-full top-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-lg'
        : 'bg-white/80 backdrop-blur-sm'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <div className="flex items-center space-x-2">
              <span className="text-xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                OurikaTravels
              </span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navItems.map((item, index) => (
              <NavItem key={index} icon={item.icon} text={item.text} href={item.href} />
            ))}
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-3">
            {/* Combined Currency & Language Selector */}
            <div className="hidden md:flex items-center">
              <PreferencesSelector
                currency={selectedCurrency}
                language={selectedLanguage}
                onClick={() => setIsPreferencesOpen(true)}
              />
            </div>

            {/* I am Guide button */}
            <button className="hidden sm:flex items-center space-x-2 bg-black !bg-black hover:!bg-gray-800 text-white rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg" style={{backgroundColor: '#000000', color: '#ffffff'}}>
              <User className="w-4 h-4 text-white" />
              <span className="text-white">I'm a Guide</span>
            </button>

            {/* Sign In button */}
            <button className="hidden sm:flex items-center space-x-2 bg-black !bg-black hover:!bg-gray-800 text-white rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg" style={{backgroundColor: '#000000', color: '#ffffff'}}>
              <span className="text-white">Sign In</span>
            </button>

            {/* Mobile menu button */}
            <button
              className="lg:hidden p-2 rounded-full bg-black !bg-black hover:!bg-gray-800 transition-colors duration-200"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle mobile menu"
              style={{backgroundColor: '#000000'}}
            >
              {isMenuOpen ? (
                <X className="w-6 h-6 text-white" />
              ) : (
                <Menu className="w-6 h-6 text-white" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`lg:hidden transition-all duration-300 ease-in-out ${
        isMenuOpen
          ? 'max-h-96 opacity-100'
          : 'max-h-0 opacity-0 overflow-hidden'
      }`}>
        <div className="bg-white/95 backdrop-blur-md border-t border-gray-200 shadow-lg">
          <div className="px-4 py-6 space-y-3">
            {navItems.map((item, index) => (
              <MobileNavItem key={index} icon={item.icon} text={item.text} href={item.href} />
            ))}

            <div className="pt-4 border-t border-gray-200">
              <div className="flex flex-col space-y-3">
                <button className="flex items-center justify-center space-x-2 w-full bg-black !bg-black hover:!bg-gray-800 text-white rounded-xl px-4 py-3 font-medium transition-all duration-200 shadow-md" style={{backgroundColor: '#000000', color: '#ffffff'}}>
                  <User className="w-5 h-5 text-white" />
                  <span className="text-white">I'm a Guide</span>
                </button>

                <button className="flex items-center justify-center space-x-2 w-full bg-black !bg-black hover:!bg-gray-800 text-white rounded-xl px-4 py-3 font-medium transition-all duration-200 shadow-md" style={{backgroundColor: '#000000', color: '#ffffff'}}>
                  <span className="text-white">Sign In</span>
                </button>

                <div className="flex items-center justify-center pt-2">
                  <PreferencesSelector
                    currency={selectedCurrency}
                    language={selectedLanguage}
                    onClick={() => setIsPreferencesOpen(true)}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Preferences Modal */}
      {isPreferencesOpen && (
        <PreferencesModal
          isOpen={isPreferencesOpen}
          onClose={() => setIsPreferencesOpen(false)}
          selectedCurrency={selectedCurrency}
          selectedLanguage={selectedLanguage}
          onCurrencyChange={setSelectedCurrency}
          onLanguageChange={setSelectedLanguage}
        />
      )}
    </nav>
  );
};

// Desktop navigation item component
const NavItem = ({ icon: Icon, text, href }) => {
  return (
    <a
      href={href}
      className="group flex items-center space-x-2 text-gray-700 hover:text-emerald-600 rounded-xl px-4 py-2 text-sm font-medium transition-all duration-200 hover:bg-gray-50"
    >
      <Icon className="w-4 h-4 transition-transform duration-200 group-hover:scale-110" />
      <span>{text}</span>
    </a>
  );
};

// Mobile navigation item component
const MobileNavItem = ({ icon: Icon, text, href }) => {
  return (
    <a
      href={href}
      className="flex items-center space-x-3 text-gray-700 hover:text-emerald-600 rounded-xl px-4 py-3 font-medium transition-all duration-200 hover:bg-gray-50 active:bg-gray-100"
    >
      <Icon className="w-5 h-5 transition-transform duration-200 group-hover:scale-110" />
      <span>{text}</span>
    </a>
  );
};



// Combined Preferences Selector Component
const PreferencesSelector = ({ currency, language, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="flex items-center space-x-2 bg-black !bg-black border-2 border-black rounded-full py-2 px-4 text-sm text-white hover:!bg-gray-800 transition-all duration-200"
      style={{backgroundColor: '#000000', color: '#ffffff'}}
    >
      <Globe className="w-4 h-4 text-white" />
      <span className="text-white">{currency} | {language}</span>
    </button>
  );
};

// Preferences Modal Component
const PreferencesModal = ({ isOpen, onClose, selectedCurrency, selectedLanguage, onCurrencyChange, onLanguageChange }) => {
  const [activeTab, setActiveTab] = useState('language');

  const currencies = ['USD', 'EUR', 'MAD', 'GBP'];
  const languages = [
    { code: 'EN', name: 'English', region: 'United States' },
    { code: 'ES', name: 'Español', region: 'España' },
    { code: 'FR', name: 'Français', region: 'France' },
    { code: 'DE', name: 'Deutsch', region: 'Deutschland' },
    { code: 'AR', name: 'العربية', region: 'المغرب' },
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Preferences</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <X className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('language')}
            className={`flex-1 py-4 px-6 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'language'
                ? 'text-black border-b-2 border-black'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Region and Language
          </button>
          <button
            onClick={() => setActiveTab('currency')}
            className={`flex-1 py-4 px-6 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'currency'
                ? 'text-black border-b-2 border-black'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Currency
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {activeTab === 'language' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose a region and language</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {languages.map((lang) => (
                  <button
                    key={lang.code}
                    onClick={() => onLanguageChange(lang.code)}
                    className={`p-4 text-left rounded-lg border-2 transition-all duration-200 ${
                      selectedLanguage === lang.code
                        ? 'border-black bg-gray-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{lang.region}</div>
                    <div className="text-sm text-gray-600">{lang.name}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'currency' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose your currency</h3>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                {currencies.map((currency) => (
                  <button
                    key={currency}
                    onClick={() => onCurrencyChange(currency)}
                    className={`p-4 text-center rounded-lg border-2 transition-all duration-200 ${
                      selectedCurrency === currency
                        ? 'border-black bg-gray-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{currency}</div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <p className="text-sm text-gray-600">
            Any changes to the preferences are optional, and will persist through your user session.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Navbar;