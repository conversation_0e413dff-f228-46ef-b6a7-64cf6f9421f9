"use client"
import { useState, useEffect } from 'react';
import {
  Gift,
  Search,
  MapPin,
  Star,
  MoreHorizontal,
  Menu,
  X,
  User,
  Globe,
  ChevronDown
} from 'lucide-react';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Force light mode on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const htmlElement = document.documentElement;
      const bodyElement = document.body;

      // Remove any dark mode classes
      htmlElement.classList.remove('dark', 'dark-mode');
      htmlElement.classList.add('light', 'light-mode');
      htmlElement.setAttribute('data-theme', 'light');
      htmlElement.style.colorScheme = 'light';

      // Set localStorage to light mode
      localStorage.setItem('theme', 'light');
    }
  }, []);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { icon: Search, text: "Discover", href: "#discover" },
    { icon: MapPin, text: "Tours", href: "#tours" },
    { icon: Star, text: "Experiences", href: "#experiences" },
    { icon: Gift, text: "Rewards", href: "#rewards" }
  ];

  return (
    <nav className={`fixed w-full top-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-lg'
        : 'bg-white/80 backdrop-blur-sm'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                <MapPin className="w-6 h-6 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                OurikaTravels
              </span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navItems.map((item, index) => (
              <NavItem key={index} icon={item.icon} text={item.text} href={item.href} />
            ))}
            <div className="mx-4 h-6 w-px bg-gray-300"></div>
            <MoreButton />
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-3">
            {/* Currency & Language */}
            <div className="hidden md:flex items-center space-x-2">
              <CurrencySelector />
              <LanguageSelector />
            </div>

            {/* I am Guide button */}
            <button className="hidden sm:flex items-center space-x-2 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105">
              <User className="w-4 h-4" />
              <span>I'm a Guide</span>
            </button>

            {/* Sign In button */}
            <button className="hidden sm:flex items-center space-x-2 bg-white border border-emerald-500 text-emerald-600 hover:bg-emerald-50 rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md">
              <span>Sign In</span>
            </button>

            {/* Mobile menu button */}
            <button
              className="lg:hidden p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              {isMenuOpen ? (
                <X className="w-6 h-6 text-gray-600" />
              ) : (
                <Menu className="w-6 h-6 text-gray-600" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`lg:hidden transition-all duration-300 ease-in-out ${
        isMenuOpen
          ? 'max-h-96 opacity-100'
          : 'max-h-0 opacity-0 overflow-hidden'
      }`}>
        <div className="bg-white/95 backdrop-blur-md border-t border-gray-200 shadow-lg">
          <div className="px-4 py-6 space-y-3">
            {navItems.map((item, index) => (
              <MobileNavItem key={index} icon={item.icon} text={item.text} href={item.href} />
            ))}

            <div className="pt-4 border-t border-gray-200">
              <div className="flex flex-col space-y-3">
                <button className="flex items-center justify-center space-x-2 w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white rounded-xl px-4 py-3 font-medium transition-all duration-200 shadow-md">
                  <User className="w-5 h-5" />
                  <span>I'm a Guide</span>
                </button>

                <button className="flex items-center justify-center space-x-2 w-full bg-white border border-emerald-500 text-emerald-600 hover:bg-emerald-50 rounded-xl px-4 py-3 font-medium transition-all duration-200 shadow-sm">
                  <span>Sign In</span>
                </button>

                <div className="flex items-center justify-between pt-2">
                  <div className="flex items-center space-x-4">
                    <CurrencySelector />
                    <LanguageSelector />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

// Desktop navigation item component
const NavItem = ({ icon: Icon, text, href }) => {
  return (
    <a
      href={href}
      className="group flex items-center space-x-2 text-gray-700 hover:text-emerald-600 rounded-xl px-4 py-2 text-sm font-medium transition-all duration-200 hover:bg-gray-50"
    >
      <Icon className="w-4 h-4 transition-transform duration-200 group-hover:scale-110" />
      <span>{text}</span>
    </a>
  );
};

// Mobile navigation item component
const MobileNavItem = ({ icon: Icon, text, href }) => {
  return (
    <a
      href={href}
      className="flex items-center space-x-3 text-gray-700 hover:text-emerald-600 rounded-xl px-4 py-3 font-medium transition-all duration-200 hover:bg-gray-50 active:bg-gray-100"
    >
      <Icon className="w-5 h-5 transition-transform duration-200 group-hover:scale-110" />
      <span>{text}</span>
    </a>
  );
};

// More button with dropdown
const MoreButton = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 text-gray-700 hover:text-emerald-600 rounded-xl px-4 py-2 text-sm font-medium transition-all duration-200 hover:bg-gray-50"
      >
        <MoreHorizontal className="w-4 h-4" />
        <span>More</span>
        <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50">
          <a href="#about" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">About Us</a>
          <a href="#contact" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Contact</a>
          <a href="#help" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Help Center</a>
        </div>
      )}
    </div>
  );
};

// Currency selector component
const CurrencySelector = () => {
  return (
    <div className="relative">
      <select className="appearance-none bg-white border border-gray-300 rounded-full py-1 px-3 pr-8 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 cursor-pointer transition-all duration-200">
        <option>USD</option>
        <option>EUR</option>
        <option>MAD</option>
        <option>GBP</option>
      </select>
      <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-500 pointer-events-none" />
    </div>
  );
};

// Language selector component
const LanguageSelector = () => {
  return (
    <div className="relative">
      <button className="flex items-center space-x-1 bg-white border border-gray-300 rounded-full py-1 px-3 text-sm text-gray-700 hover:bg-gray-50 transition-all duration-200">
        <Globe className="w-4 h-4" />
        <span className="hidden sm:inline">EN</span>
        <ChevronDown className="w-3 h-3" />
      </button>
    </div>
  );
};

export default Navbar;