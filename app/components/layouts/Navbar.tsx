"use client"
import { useState, useEffect } from 'react';
import {
  Gift,
  Search,
  MapPin,
  Star,
  MoreHorizontal,
  Menu,
  X,
  User,
  Globe,
  ChevronDown
} from 'lucide-react';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Force light mode on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const htmlElement = document.documentElement;
      const bodyElement = document.body;

      // Remove any dark mode classes
      htmlElement.classList.remove('dark', 'dark-mode');
      htmlElement.classList.add('light', 'light-mode');
      htmlElement.setAttribute('data-theme', 'light');
      htmlElement.style.colorScheme = 'light';

      // Set localStorage to light mode
      localStorage.setItem('theme', 'light');
    }
  }, []);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { icon: Search, text: "Discover", href: "#discover" },
    { icon: MapPin, text: "Tours", href: "#tours" },
    { icon: Star, text: "Experiences", href: "#experiences" },
    { icon: Gift, text: "Rewards", href: "#rewards" }
  ];

  return (
    <nav className={`fixed w-full top-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-lg'
        : 'bg-white/80 backdrop-blur-sm'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <div className="flex items-center space-x-2">
              <span className="text-xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                OurikaTravels
              </span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navItems.map((item, index) => (
              <NavItem key={index} icon={item.icon} text={item.text} href={item.href} />
            ))}
            <div className="mx-4 h-6 w-px bg-gray-300"></div>
            <MoreButton />
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-3">
            {/* Currency & Language */}
            <div className="hidden md:flex items-center space-x-2">
              <CurrencySelector />
              <LanguageSelector />
            </div>

            {/* I am Guide button */}
            <button className="hidden sm:flex items-center space-x-2 bg-black !bg-black hover:!bg-gray-800 text-white rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg" style={{backgroundColor: '#000000', color: '#ffffff'}}>
              <User className="w-4 h-4 text-white" />
              <span className="text-white">I'm a Guide</span>
            </button>

            {/* Sign In button */}
            <button className="hidden sm:flex items-center space-x-2 bg-black !bg-black hover:!bg-gray-800 text-white rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg" style={{backgroundColor: '#000000', color: '#ffffff'}}>
              <span className="text-white">Sign In</span>
            </button>

            {/* Mobile menu button */}
            <button
              className="lg:hidden p-2 rounded-full bg-black !bg-black hover:!bg-gray-800 transition-colors duration-200"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle mobile menu"
              style={{backgroundColor: '#000000'}}
            >
              {isMenuOpen ? (
                <X className="w-6 h-6 text-white" />
              ) : (
                <Menu className="w-6 h-6 text-white" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`lg:hidden transition-all duration-300 ease-in-out ${
        isMenuOpen
          ? 'max-h-96 opacity-100'
          : 'max-h-0 opacity-0 overflow-hidden'
      }`}>
        <div className="bg-white/95 backdrop-blur-md border-t border-gray-200 shadow-lg">
          <div className="px-4 py-6 space-y-3">
            {navItems.map((item, index) => (
              <MobileNavItem key={index} icon={item.icon} text={item.text} href={item.href} />
            ))}

            <div className="pt-4 border-t border-gray-200">
              <div className="flex flex-col space-y-3">
                <button className="flex items-center justify-center space-x-2 w-full bg-black hover:bg-gray-800 text-white rounded-xl px-4 py-3 font-medium transition-all duration-200 shadow-md">
                  <User className="w-5 h-5" />
                  <span>I'm a Guide</span>
                </button>

                <button className="flex items-center justify-center space-x-2 w-full bg-black hover:bg-gray-800 text-white rounded-xl px-4 py-3 font-medium transition-all duration-200 shadow-md">
                  <span>Sign In</span>
                </button>

                <div className="flex items-center justify-between pt-2">
                  <div className="flex items-center space-x-4">
                    <CurrencySelector />
                    <LanguageSelector />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

// Desktop navigation item component
const NavItem = ({ icon: Icon, text, href }) => {
  return (
    <a
      href={href}
      className="group flex items-center space-x-2 text-gray-700 hover:text-emerald-600 rounded-xl px-4 py-2 text-sm font-medium transition-all duration-200 hover:bg-gray-50"
    >
      <Icon className="w-4 h-4 transition-transform duration-200 group-hover:scale-110" />
      <span>{text}</span>
    </a>
  );
};

// Mobile navigation item component
const MobileNavItem = ({ icon: Icon, text, href }) => {
  return (
    <a
      href={href}
      className="flex items-center space-x-3 text-gray-700 hover:text-emerald-600 rounded-xl px-4 py-3 font-medium transition-all duration-200 hover:bg-gray-50 active:bg-gray-100"
    >
      <Icon className="w-5 h-5 transition-transform duration-200 group-hover:scale-110" />
      <span>{text}</span>
    </a>
  );
};

// More button with dropdown
const MoreButton = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 text-gray-700 hover:text-emerald-600 rounded-xl px-4 py-2 text-sm font-medium transition-all duration-200 hover:bg-gray-50"
      >
        <MoreHorizontal className="w-4 h-4" />
        <span>More</span>
        <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50">
          <a href="#about" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">About Us</a>
          <a href="#contact" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Contact</a>
          <a href="#help" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Help Center</a>
        </div>
      )}
    </div>
  );
};

// Currency selector component
const CurrencySelector = () => {
  return (
    <div className="relative">
      <select className="appearance-none bg-black !bg-black border-2 border-black rounded-full py-2 px-4 pr-8 text-sm text-white focus:outline-none focus:ring-2 focus:ring-gray-600 cursor-pointer transition-all duration-200 hover:!bg-gray-800" style={{backgroundColor: '#000000', color: '#ffffff'}}>
        <option className="bg-black text-white" style={{backgroundColor: '#000000', color: '#ffffff'}}>USD</option>
        <option className="bg-black text-white" style={{backgroundColor: '#000000', color: '#ffffff'}}>EUR</option>
        <option className="bg-black text-white" style={{backgroundColor: '#000000', color: '#ffffff'}}>MAD</option>
        <option className="bg-black text-white" style={{backgroundColor: '#000000', color: '#ffffff'}}>GBP</option>
      </select>
      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white pointer-events-none" />
    </div>
  );
};

// Language selector component
const LanguageSelector = () => {
  return (
    <div className="relative">
      <button className="flex items-center space-x-2 bg-black !bg-black border-2 border-black rounded-full py-2 px-4 text-sm text-white hover:!bg-gray-800 transition-all duration-200" style={{backgroundColor: '#000000', color: '#ffffff'}}>
        <Globe className="w-4 h-4 text-white" />
        <span className="hidden sm:inline text-white">EN</span>
        <ChevronDown className="w-4 h-4 text-white" />
      </button>
    </div>
  );
};

export default Navbar;