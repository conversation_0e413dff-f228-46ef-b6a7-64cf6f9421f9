"use client"
import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Globe, X } from 'lucide-react';

interface PreferencesSelectorProps {
  currency: string;
  language: string;
  onClick: () => void;
}

interface PreferencesModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCurrency: string;
  selectedLanguage: string;
  onCurrencyChange: (currency: string) => void;
  onLanguageChange: (language: string) => void;
}

// Combined Preferences Selector Component
export const PreferencesSelector = ({ currency, language, onClick }: PreferencesSelectorProps) => {
  return (
    <button 
      onClick={onClick}
      className="flex items-center space-x-2 bg-black !bg-black border-2 border-black rounded-full py-2 px-4 text-sm text-white hover:!bg-gray-800 transition-all duration-200"
      style={{backgroundColor: '#000000', color: '#ffffff'}}
    >
      <Globe className="w-4 h-4 text-white" />
      <span className="text-white">{currency} | {language}</span>
    </button>
  );
};

// Preferences Modal Component
export const PreferencesModal = ({
  isOpen,
  onClose,
  selectedCurrency,
  selectedLanguage,
  onCurrencyChange,
  onLanguageChange
}: PreferencesModalProps) => {
  const [activeTab, setActiveTab] = useState('language');
  const [mounted, setMounted] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const currencies = ['USD', 'EUR', 'MAD', 'GBP'];
  const languages = [
    { code: 'EN', name: 'English', region: 'United States' },
    { code: 'ES', name: 'Español', region: 'España' },
    { code: 'FR', name: 'Français', region: 'France' },
    { code: 'DE', name: 'Deutsch', region: 'Deutschland' },
    { code: 'AR', name: 'العربية', region: 'المغرب' },
  ];

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      setIsVisible(false);
      // Restore body scroll when modal is closed
      document.body.style.overflow = 'unset';
    }

    // Cleanup function to restore scroll on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen || !mounted) return null;

  const modalContent = (
    <div
      className={`fixed inset-0 backdrop-blur-sm flex items-center justify-center z-[9999] transition-all duration-300 ease-out ${
        isVisible ? 'bg-black bg-opacity-40' : 'bg-black bg-opacity-0'
      }`}
      style={{
        zIndex: 9999,
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: isVisible ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0)'
      }}
      onClick={onClose}
    >
      <div
        className={`!bg-white rounded-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden relative z-[10000] transform transition-all duration-300 ease-out ${
          isVisible ? 'scale-100 opacity-100 translate-y-0' : 'scale-95 opacity-0 translate-y-4'
        }`}
        style={{
          zIndex: 10000,
          backgroundColor: '#ffffff !important',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05), 0 10px 25px rgba(0, 0, 0, 0.1), 0 20px 40px rgba(0, 0, 0, 0.15)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 !bg-white" style={{ backgroundColor: '#ffffff !important' }}>
          <h2 className="text-2xl font-bold !text-black" style={{ color: '#000000 !important' }}>Preferences</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <X className="w-6 h-6 !text-black" style={{ color: '#000000 !important' }} />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 !bg-white" style={{ backgroundColor: '#ffffff !important' }}>
          <button
            onClick={() => setActiveTab('language')}
            className={`flex-1 py-4 px-6 text-sm font-medium transition-colors duration-200 !bg-white ${
              activeTab === 'language'
                ? '!text-black border-b-2 border-black'
                : '!text-gray-600 hover:!text-black'
            }`}
            style={{
              backgroundColor: '#ffffff !important',
              color: activeTab === 'language' ? '#000000 !important' : '#6b7280 !important'
            }}
          >
            Region and Language
          </button>
          <button
            onClick={() => setActiveTab('currency')}
            className={`flex-1 py-4 px-6 text-sm font-medium transition-colors duration-200 !bg-white ${
              activeTab === 'currency'
                ? '!text-black border-b-2 border-black'
                : '!text-gray-600 hover:!text-black'
            }`}
            style={{
              backgroundColor: '#ffffff !important',
              color: activeTab === 'currency' ? '#000000 !important' : '#6b7280 !important'
            }}
          >
            Currency
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-96 overflow-y-auto !bg-white" style={{ backgroundColor: '#ffffff !important' }}>
          {activeTab === 'language' && (
            <div>
              <h3 className="text-lg font-semibold !text-black mb-4" style={{ color: '#000000 !important' }}>Choose a region and language</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {languages.map((lang) => (
                  <button
                    key={lang.code}
                    onClick={() => onLanguageChange(lang.code)}
                    className={`p-4 text-left rounded-lg border-2 transition-all duration-200 !bg-white ${
                      selectedLanguage === lang.code
                        ? 'border-black !bg-gray-50'
                        : 'border-gray-200 hover:border-gray-300 hover:!bg-gray-50'
                    }`}
                    style={{
                      backgroundColor: selectedLanguage === lang.code ? '#f9fafb !important' : '#ffffff !important'
                    }}
                  >
                    <div className="font-medium !text-black" style={{ color: '#000000 !important' }}>{lang.region}</div>
                    <div className="text-sm !text-gray-600" style={{ color: '#6b7280 !important' }}>{lang.name}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'currency' && (
            <div>
              <h3 className="text-lg font-semibold !text-black mb-4" style={{ color: '#000000 !important' }}>Choose your currency</h3>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                {currencies.map((currency) => (
                  <button
                    key={currency}
                    onClick={() => onCurrencyChange(currency)}
                    className={`p-4 text-center rounded-lg border-2 transition-all duration-200 !bg-white ${
                      selectedCurrency === currency
                        ? 'border-black !bg-gray-50'
                        : 'border-gray-200 hover:border-gray-300 hover:!bg-gray-50'
                    }`}
                    style={{
                      backgroundColor: selectedCurrency === currency ? '#f9fafb !important' : '#ffffff !important'
                    }}
                  >
                    <div className="font-medium !text-black" style={{ color: '#000000 !important' }}>{currency}</div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 !bg-white" style={{ backgroundColor: '#ffffff !important' }}>
          <p className="text-sm !text-gray-600" style={{ color: '#6b7280 !important' }}>
            Any changes to the preferences are optional, and will persist through your user session.
          </p>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};
