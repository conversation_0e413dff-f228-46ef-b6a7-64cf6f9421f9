"use client"
import { useState } from 'react';
import { Globe, X } from 'lucide-react';

interface PreferencesSelectorProps {
  currency: string;
  language: string;
  onClick: () => void;
}

interface PreferencesModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCurrency: string;
  selectedLanguage: string;
  onCurrencyChange: (currency: string) => void;
  onLanguageChange: (language: string) => void;
}

// Combined Preferences Selector Component
export const PreferencesSelector = ({ currency, language, onClick }: PreferencesSelectorProps) => {
  return (
    <button 
      onClick={onClick}
      className="flex items-center space-x-2 bg-black !bg-black border-2 border-black rounded-full py-2 px-4 text-sm text-white hover:!bg-gray-800 transition-all duration-200"
      style={{backgroundColor: '#000000', color: '#ffffff'}}
    >
      <Globe className="w-4 h-4 text-white" />
      <span className="text-white">{currency} | {language}</span>
    </button>
  );
};

// Preferences Modal Component
export const PreferencesModal = ({ 
  isOpen, 
  onClose, 
  selectedCurrency, 
  selectedLanguage, 
  onCurrencyChange, 
  onLanguageChange 
}: PreferencesModalProps) => {
  const [activeTab, setActiveTab] = useState('language');

  const currencies = ['USD', 'EUR', 'MAD', 'GBP'];
  const languages = [
    { code: 'EN', name: 'English', region: 'United States' },
    { code: 'ES', name: 'Español', region: 'España' },
    { code: 'FR', name: 'Français', region: 'France' },
    { code: 'DE', name: 'Deutsch', region: 'Deutschland' },
    { code: 'AR', name: 'العربية', region: 'المغرب' },
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Preferences</h2>
          <button 
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <X className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('language')}
            className={`flex-1 py-4 px-6 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'language' 
                ? 'text-black border-b-2 border-black' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Region and Language
          </button>
          <button
            onClick={() => setActiveTab('currency')}
            className={`flex-1 py-4 px-6 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'currency' 
                ? 'text-black border-b-2 border-black' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Currency
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {activeTab === 'language' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose a region and language</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {languages.map((lang) => (
                  <button
                    key={lang.code}
                    onClick={() => onLanguageChange(lang.code)}
                    className={`p-4 text-left rounded-lg border-2 transition-all duration-200 ${
                      selectedLanguage === lang.code
                        ? 'border-black bg-gray-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{lang.region}</div>
                    <div className="text-sm text-gray-600">{lang.name}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'currency' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose your currency</h3>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                {currencies.map((currency) => (
                  <button
                    key={currency}
                    onClick={() => onCurrencyChange(currency)}
                    className={`p-4 text-center rounded-lg border-2 transition-all duration-200 ${
                      selectedCurrency === currency
                        ? 'border-black bg-gray-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{currency}</div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <p className="text-sm text-gray-600">
            Any changes to the preferences are optional, and will persist through your user session.
          </p>
        </div>
      </div>
    </div>
  );
};
