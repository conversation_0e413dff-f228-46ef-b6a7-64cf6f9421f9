interface PriceDisplayProps {
  originalPrice?: number;
  currentPrice: number;
  priceUnit: string;
}

const PriceDisplay = ({ originalPrice, currentPrice, priceUnit }: PriceDisplayProps) => {
  const savingsPercentage = originalPrice 
    ? Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
    : 0;

  return (
    <div className="flex items-end justify-between">
      <div>
        {originalPrice && (
          <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">
            From <span className="line-through">{originalPrice}د.م</span>
          </div>
        )}
        <div className="flex items-baseline gap-1">
          <span className="text-xl font-bold text-red-600 dark:text-red-400">
            {currentPrice}د.م
          </span>
          <span className="text-sm text-gray-600 dark:text-gray-300">
            {priceUnit}
          </span>
        </div>
      </div>
      
      {originalPrice && (
        <div className="bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 px-2 py-1 rounded text-xs font-medium">
          Save {savingsPercentage}%
        </div>
      )}
    </div>
  );
};

export default PriceDisplay;