"use client"
import { Search } from 'lucide-react';

interface SearchInputProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  placeholder?: string;
}

const SearchInput = ({ searchQuery, onSearchChange, placeholder = "Places to go, things to do, hotels..." }: SearchInputProps) => {
  return (
    <div className="relative">
      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
        <Search className="h-5 w-5 text-gray-400" />
      </div>
      <input
        type="text"
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
        placeholder={placeholder}
        className="w-full pl-12 pr-24 sm:pr-32 py-4 text-lg bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
      />
      <div className="absolute inset-y-0 right-0 flex items-center pr-2">
        <button className="bg-emerald-500 hover:bg-emerald-600 text-white font-medium py-2 px-4 sm:px-6 rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg">
          Search
        </button>
      </div>
    </div>
  );
};

export default SearchInput;