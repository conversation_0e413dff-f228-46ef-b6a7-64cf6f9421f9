"use client"
import { Search, MapPin, Star, Gift, MoreHorizontal, User } from 'lucide-react';

interface SearchTab {
  id: string;
  label: string;
  icon: any;
}

interface SearchTabsProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const SearchTabs = ({ activeTab, onTabChange }: SearchTabsProps) => {
  const searchTabs: SearchTab[] = [
    { id: 'all', label: 'Search All', icon: Search },
    { id: 'hotels', label: 'Hotels', icon: MapPin },
    { id: 'experiences', label: 'Things to Do', icon: Star },
    { id: 'restaurants', label: 'Restaurants', icon: Gift },
    { id: 'flights', label: 'Flights', icon: MoreHorizontal },
    { id: 'rentals', label: 'Vacation Rentals', icon: User }
  ];

  return (
    <div className="flex flex-wrap gap-2 sm:gap-4 mb-6 border-b border-gray-200 dark:border-gray-700 pb-4">
      {searchTabs.map((tab) => {
        const Icon = tab.icon;
        return (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`flex items-center space-x-2 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-emerald-500 text-white shadow-md'
                : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
          >
            <Icon className="w-4 h-4" />
            <span className="hidden sm:inline">{tab.label}</span>
            <span className="sm:hidden">{tab.label.split(' ')[0]}</span>
          </button>
        );
      })}
    </div>
  );
};

export default SearchTabs;