const TrustIndicators = () => {
  const indicators = [
    {
      value: "50,000+",
      label: "Happy Travelers"
    },
    {
      value: "4.9★",
      label: "Average Rating"
    },
    {
      value: "98%",
      label: "Would Recommend"
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8">
      {indicators.map((indicator, index) => (
        <div key={index} className="text-center p-6 bg-white dark:bg-gray-900 rounded-2xl shadow-lg">
          <div className="text-3xl sm:text-4xl font-bold text-emerald-600 dark:text-emerald-400 mb-2">
            {indicator.value}
          </div>
          <div className="text-gray-600 dark:text-gray-300 font-medium">
            {indicator.label}
          </div>
        </div>
      ))}
    </div>
  );
};

export default TrustIndicators;