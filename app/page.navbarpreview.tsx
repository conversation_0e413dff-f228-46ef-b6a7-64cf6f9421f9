"use client"

import Navbar from './components/layouts/Navbar';

export default function NavbarPreview() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-all duration-500">
      <Navbar />
      
      {/* Content to test scrolling and dark mode */}
      <div className="pt-20 px-4 max-w-4xl mx-auto">
        <div className="space-y-8">
          <section className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-500">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4 transition-colors duration-500">
              Dark Mode Toggle Test
            </h1>
            <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed transition-colors duration-500">
              This page is designed to test the dark mode toggle functionality in the navbar. 
              Click the sun/moon icon in the top right corner to switch between light and dark themes.
              The background should change from white to dark gray, and text should invert colors.
              <br /><br />
              <strong>Mobile Test:</strong> On mobile devices, open the hamburger menu to access the theme toggle within the mobile menu.
            </p>
            <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg transition-all duration-500">
              <p className="text-sm text-gray-700 dark:text-gray-200 transition-colors duration-500">
                <strong>Test Status:</strong> If you can see this box change colors when toggling themes, the dark mode is working correctly!
              </p>
            </div>
          </section>

          <section className="bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl p-8 text-white">
            <h2 className="text-3xl font-bold mb-4">Ourika Valley Adventures</h2>
            <p className="text-emerald-100 text-lg leading-relaxed">
              Discover the breathtaking beauty of Morocco's hidden valley with our expert local guides. 
              Experience authentic Berber culture, stunning waterfalls, and majestic Atlas Mountain views.
            </p>
          </section>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-500">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3 transition-colors duration-500">
                Mountain Hiking Tours
              </h3>
              <p className="text-gray-600 dark:text-gray-300 transition-colors duration-500">
                Explore scenic trails through traditional Berber villages and witness stunning mountain landscapes.
              </p>
              <div className="mt-4 w-full h-2 bg-emerald-200 dark:bg-emerald-800 rounded-full transition-all duration-500">
                <div className="h-full w-3/4 bg-emerald-500 rounded-full"></div>
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-500">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3 transition-colors duration-500">
                Cultural Experiences
              </h3>
              <p className="text-gray-600 dark:text-gray-300 transition-colors duration-500">
                Immerse yourself in authentic Moroccan culture with local family visits and traditional meals.
              </p>
              <div className="mt-4 w-full h-2 bg-teal-200 dark:bg-teal-800 rounded-full transition-all duration-500">
                <div className="h-full w-2/3 bg-teal-500 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Add more content to test scrolling effect */}
          <section className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg transition-colors duration-300">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Navigation Features
            </h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                <span className="text-gray-700 dark:text-gray-300">Discover - Find amazing destinations</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                <span className="text-gray-700 dark:text-gray-300">Tours - Browse available tour packages</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                <span className="text-gray-700 dark:text-gray-300">Experiences - Unique cultural activities</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                <span className="text-gray-700 dark:text-gray-300">Rewards - Loyalty program benefits</span>
              </div>
            </div>
          </section>

          {/* Spacer content to test scroll effects */}
          <div className="h-96 bg-gradient-to-b from-emerald-50 to-teal-50 dark:from-gray-800 dark:to-gray-900 rounded-xl flex items-center justify-center transition-colors duration-300">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Scroll to Test Navbar Effects
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                The navbar should become more opaque as you scroll down
              </p>
            </div>
          </div>

          <div className="h-96 bg-gradient-to-b from-amber-50 to-orange-50 dark:from-gray-800 dark:to-gray-900 rounded-xl flex items-center justify-center transition-colors duration-300">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                More Scroll Content
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Keep scrolling to see the navbar backdrop blur effect
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}